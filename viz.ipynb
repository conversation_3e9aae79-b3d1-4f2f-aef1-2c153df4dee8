{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import plotly.graph_objects as go\n", "from plotly.subplots import make_subplots\n", "from scipy import stats\n", "\n", "\n", "\n", "\n", "\n", "a = pd.read_parquet(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2025\\others\\SOLD_UNITS_vs_DELIVERED_UNITS\\24w26_25w26.parquet\")\n", "\n", "\n", "df_country = a.groupby(['country','week','dep name'], as_index=False)[[\"sold_units\", \"unit_delivered\"]].sum()\n", "\n", "df_total = df_country.groupby([\"country\", \"week\"])[['sold_units', 'unit_delivered']].sum().reset_index()\n", "\n", "\n", "\n", "\n", "\n", "\n", "def combine_plotly_figs_to_html(plotly_figs, html_fname, include_plotlyjs='cdn', \n", "                            separator=None, auto_open=False):\n", "    with open(html_fname, 'w') as f:\n", "        f.write(plotly_figs[0].to_html(include_plotlyjs=include_plotlyjs))\n", "        for fig in plotly_figs[1:]:\n", "            if separator:\n", "                f.write(separator)\n", "            f.write(fig.to_html(full_html=False, include_plotlyjs=False))\n", "\n", "    if auto_open:\n", "        import pathlib, webbrowser\n", "        uri = pathlib.Path(html_fname).absolute().as_uri()\n", "        webbrowser.open(uri)\n", "\n", "def parse_custom_week(week_str):\n", "    year = int(week_str[1:5])\n", "    week = int(week_str[6:8])\n", "    return year * 100 + week\n", "\n", "def format_week_label(week_str):\n", "    return f\"w{week_str[6:8]}\"\n", "\n", "def add_trendline(x, y):\n", "    x_numeric = x.apply(parse_custom_week)\n", "    slope, intercept, r_value, p_value, std_err = stats.linregress(x_numeric, y)\n", "    line = slope * x_numeric + intercept\n", "    return x, line\n", "\n", "def format_large_number(num):\n", "    if num >= 1_000_000:\n", "        return f\"{num/1_000_000:.1f}M\"\n", "    elif num >= 1_000:\n", "        return f\"{num/1_000:.1f}K\"\n", "    else:\n", "        return f\"{num:.0f}\"\n", "\n", "# Cool color palette\n", "colors = {'CZ': ['#4e79a7', '#a0cbe8'], 'HU': ['#59a14f', '#8cd17d'], 'SK': ['#9c755f', '#c9b18f']}\n", "cont = []\n", "\n", "# Create the Total chart with dark theme\n", "fig_total = make_subplots(rows=len(df_total['country'].unique()), cols=1, \n", "                          shared_xaxes=True,\n", "                          vertical_spacing=0.02,\n", "                          subplot_titles=[\"\" for _ in df_total['country'].unique()])\n", "\n", "for i, country in enumerate(df_total['country'].unique(), start=1):\n", "    df_country_filtered = df_total[df_total['country'] == country]\n", "    \n", "    week_labels = df_country_filtered[\"week\"].apply(format_week_label)\n", "    \n", "    fig_total.add_trace(\n", "        go.Bar(x=week_labels, y=df_country_filtered[\"sold_units\"],\n", "            name=\"Sold Units\", marker_color=colors[country][0],\n", "            showlegend=(i==1), opacity=0.8,\n", "            hovertemplate=\"<b>Sold Units</b><br>Week: %{x}<br>Units: %{customdata}<extra></extra>\",\n", "            customdata=[format_large_number(x) for x in df_country_filtered[\"sold_units\"]],\n", "            offset=-0.2, width=0.4),\n", "        row=i, col=1\n", "    )\n", "    \n", "    fig_total.add_trace(\n", "        go.Bar(x=week_labels, y=df_country_filtered[\"unit_delivered\"],\n", "            name=\"Units Delivered\", marker_color=colors[country][1],\n", "            showlegend=(i==1), opacity=0.8,\n", "            hovertemplate=\"<b>Units Delivered</b><br>Week: %{x}<br>Units: %{customdata}<extra></extra>\",\n", "            customdata=[format_large_number(x) for x in df_country_filtered[\"unit_delivered\"]],\n", "            offset=0.2, width=0.4),\n", "        row=i, col=1\n", "    )\n", "    \n", "    for column, color, name in [(\"sold_units\", \"#FFA500\", \"Sold Units Trend\"), \n", "                                (\"unit_delivered\", \"#FF4136\", \"Units Delivered Trend\")]:\n", "        x_trend, y_trend = add_trendline(df_country_filtered[\"week\"], df_country_filtered[column])\n", "        fig_total.add_trace(\n", "            go.Scatter(x=x_trend.apply(format_week_label), y=y_trend, mode='lines', name=name,\n", "                       line=dict(color=color, width=1.5, dash='solid'), showlegend=(i==1),\n", "                       hovertemplate=f\"<b>{name}</b><br>Week: %{{x}}<br>Units: %{{customdata}}<extra></extra>\",\n", "                       customdata=[format_large_number(x) for x in y_trend]),\n", "            row=i, col=1\n", "        )\n", "\n", "fig_total.update_layout(\n", "    title_text=\"<b>Sales and Deliveries - Total</b>\",\n", "    title_font=dict(size=24, color='white'),\n", "    barmode='overlay',\n", "    bargap=0,\n", "    bargroupgap=0,\n", "    legend=dict(orientation=\"h\", yanchor=\"bottom\", y=1.02, xanchor=\"right\", x=1, font=dict(color='white')),\n", "    showlegend=True,\n", "    margin=dict(l=100, r=100, t=100, b=50),\n", "    plot_bgcolor='#1E1E1E',\n", "    paper_bgcolor='#121212',\n", "    font=dict(family=\"Arial, sans-serif\", size=12, color=\"white\"),\n", "    hoverlabel=dict(bgcolor=\"white\", font_size=12, font_color=\"black\")\n", ")\n", "\n", "fig_total.update_xaxes(\n", "    title_text=\"\",\n", "    tickangle=45,\n", "    gridcolor='#333333',\n", "    tickfont=dict(size=12, color='white'),\n", "    title_font=dict(size=12, color='white')\n", ")\n", "\n", "fig_total.update_yaxes(\n", "    title_text=\"\",\n", "    gridcolor='#333333',\n", "    tickformat=',d',\n", "    title_font=dict(size=10, color='white'),\n", "    tickfont=dict(size=12, color='white')\n", ")\n", "\n", "for i in range(1, len(df_total['country'].unique())):\n", "    fig_total.update_xaxes(title_text=\"\", row=i, col=1)\n", "\n", "for i, country in enumerate(df_total['country'].unique(), start=1):\n", "    fig_total.update_xaxes(showline=True, linewidth=1, linecolor='#444444', mirror=True, row=i, col=1)\n", "    fig_total.update_yaxes(showline=True, linewidth=1, linecolor='#444444', mirror=True, row=i, col=1)\n", "    \n", "    fig_total.add_annotation(\n", "        text=f\"<b>{country}</b>\",\n", "        xref=\"paper\", yref=\"paper\",\n", "        x=1.02, y=1 - (i-1)/len(df_total['country'].unique()) - 0.5/len(df_total['country'].unique()),\n", "        showarrow=False,\n", "        font=dict(size=14, color='white'),\n", "        xanchor=\"left\", yanchor=\"middle\"\n", "    )\n", "\n", "cont.append(fig_total)\n", "\n", "# Original script for individual departments\n", "for x in df_country['dep name'].unique().tolist():\n", "\n", "    # Filter the dataframe for the current department\n", "    df_filtered = df_country[df_country['dep name'] == x].copy()\n", "    \n", "    # Get unique countries for this department\n", "    countries = df_filtered['country'].unique()\n", "\n", "    fig = make_subplots(rows=len(countries), cols=1, \n", "                        shared_xaxes=True,\n", "                        vertical_spacing=0.02,\n", "                        subplot_titles=[\"\" for _ in countries])\n", "\n", "    for i, country in enumerate(countries, start=1):\n", "        df_country_filtered = df_filtered[df_filtered['country'] == country]\n", "        \n", "        week_labels = df_country_filtered[\"week\"].apply(format_week_label)\n", "        \n", "        fig.add_trace(\n", "            go.Bar(x=week_labels, y=df_country_filtered[\"sold_units\"],\n", "                name=\"Sold Units\", marker_color=colors[country][0],\n", "                showlegend=(i==1), opacity=0.8,\n", "                hovertemplate=\"<b>Sold Units</b><br>Week: %{x}<br>Units: %{customdata}<extra></extra>\",\n", "                customdata=[format_large_number(x) for x in df_country_filtered[\"sold_units\"]],\n", "                offset=-0.2, width=0.4),\n", "            row=i, col=1\n", "        )\n", "        \n", "        fig.add_trace(\n", "            go.Bar(x=week_labels, y=df_country_filtered[\"unit_delivered\"],\n", "                name=\"Units Delivered\", marker_color=colors[country][1],\n", "                showlegend=(i==1), opacity=0.8,\n", "                hovertemplate=\"<b>Units Delivered</b><br>Week: %{x}<br>Units: %{customdata}<extra></extra>\",\n", "                customdata=[format_large_number(x) for x in df_country_filtered[\"unit_delivered\"]],\n", "                offset=0.2, width=0.4),\n", "            row=i, col=1\n", "        )\n", "        \n", "        for column, color, name in [(\"sold_units\", \"orange\", \"Sold Units Trend\"), \n", "                                    (\"unit_delivered\", \"red\", \"Units Delivered Trend\")]:\n", "            x_trend, y_trend = add_trendline(df_country_filtered[\"week\"], df_country_filtered[column])\n", "            fig.add_trace(\n", "                go.Scatter(x=x_trend.apply(format_week_label), y=y_trend, mode='lines', name=name,\n", "                           line=dict(color=color, width=1.5, dash='solid'), showlegend=(i==1),\n", "                           hovertemplate=f\"<b>{name}</b><br>Week: %{{x}}<br>Units: %{{customdata}}<extra></extra>\",\n", "                           customdata=[format_large_number(x) for x in y_trend]),\n", "                row=i, col=1\n", "            )\n", "\n", "    fig.update_layout(\n", "        title_text=f\"<b>Sales and Deliveries - {x}</b>\",\n", "        title_font=dict(size=24),\n", "        barmode='overlay',\n", "        bargap=0,\n", "        bargroupgap=0,\n", "        legend=dict(orientation=\"h\", yanchor=\"bottom\", y=1.02, xanchor=\"right\", x=1),\n", "        showlegend=True,\n", "        margin=dict(l=100, r=100, t=100, b=50),\n", "        plot_bgcolor='white',\n", "        paper_bgcolor='white',\n", "        font=dict(family=\"Arial, sans-serif\", size=12, color=\"#333333\"),\n", "        hoverlabel=dict(bgcolor=\"black\", font_size=12, font_color=\"white\")\n", "    )\n", "\n", "    fig.update_xaxes(\n", "        title_text=\"\",\n", "        tickangle=45,\n", "        gridcolor='#f0f0f0',\n", "        tickfont=dict(size=12),\n", "        title_font=dict(size=12)\n", "    )\n", "\n", "    fig.update_yaxes(\n", "        title_text=\"\",\n", "        gridcolor='#f0f0f0',\n", "        tickformat=',d',\n", "        title_font=dict(size=10),\n", "        tickfont=dict(size=12)\n", "    )\n", "\n", "    for i in range(1, len(countries)):\n", "        fig.update_xaxes(title_text=\"\", row=i, col=1)\n", "\n", "    for i, country in enumerate(countries, start=1):\n", "        fig.update_xaxes(showline=True, linewidth=1, linecolor='#e0e0e0', mirror=True, row=i, col=1)\n", "        fig.update_yaxes(showline=True, linewidth=1, linecolor='#e0e0e0', mirror=True, row=i, col=1)\n", "        \n", "        fig.add_annotation(\n", "            text=f\"<b>{country}</b>\",\n", "            xref=\"paper\", yref=\"paper\",\n", "            x=1.02, y=1 - (i-1)/len(countries) - 0.5/len(countries),\n", "            showarrow=False,\n", "            font=dict(size=14),\n", "            xanchor=\"left\", yanchor=\"middle\"\n", "        )\n", "\n", "    cont.append(fig)\n", "\n", "# Generate the HTML file\n", "html_fname = r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2025\\others\\SOLD_UNITS_vs_DELIVERED_UNITS\\Sales_and_Delivers_24w26-25w26.html\"\n", "plotly_figs = cont\n", "combine_plotly_figs_to_html(plotly_figs, html_fname, include_plotlyjs='cdn', \n", "                        separator=None, auto_open=False)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 2}