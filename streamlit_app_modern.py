import streamlit as st
import pandas as pd
import numpy as np
from scipy import stats
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import io
from datetime import datetime

# Modern page configuration with sidebar state management
st.set_page_config(
    page_title="Sales & Deliveries | Modern Dashboard",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Initialize session state for sidebar management
if 'sidebar_state' not in st.session_state:
    st.session_state.sidebar_state = 'expanded'

# Modern dark theme CSS with professional colors
st.markdown("""
<style>
    /* Import modern fonts */
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
    
    /* Root variables for consistent professional theming */
    :root {
        --bg-primary: #0a0e1a;
        --bg-secondary: #1e2139;
        --bg-card: #2a2d4a;
        --bg-hover: #363b5e;
        --text-primary: #ffffff;
        --text-secondary: #b8bcc8;
        --accent-primary: #4f46e5;
        --accent-secondary: #6366f1;
        --accent-tertiary: #8b5cf6;
        --success: #10b981;
        --warning: #f59e0b;
        --error: #ef4444;
        --border: #374151;
        --shadow: 0 4px 24px rgba(0, 0, 0, 0.15);
        --gradient-primary: linear-gradient(135deg, #4f46e5 0%, #6366f1 100%);
        --gradient-secondary: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
        --gradient-card: linear-gradient(135deg, #2a2d4a 0%, #363b5e 100%);
    }
    
    /* Global styles */
    .stApp {
        background: var(--bg-primary);
        color: var(--text-primary);
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    }
    

    
    /* Main header */
    .main-header {
        background: var(--gradient-primary);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        font-size: 2.5rem;
        font-weight: 700;
        text-align: center;
        margin-bottom: 1.5rem;
        letter-spacing: -0.02em;
    }
    
    /* Modern metric cards */
    .metric-card {
        background: var(--gradient-card);
        border: 1px solid var(--border);
        border-radius: 16px;
        padding: 1.5rem;
        margin: 0.5rem 0;
        box-shadow: var(--shadow);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
    }
    
    .metric-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: var(--gradient-primary);
        opacity: 0;
        transition: opacity 0.3s ease;
    }
    
    .metric-card:hover::before {
        opacity: 1;
    }
    
    .metric-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 32px rgba(79, 70, 229, 0.2);
        border-color: var(--accent-primary);
    }
    
    .metric-label {
        font-size: 0.85rem;
        color: var(--text-secondary);
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-bottom: 0.5rem;
    }
    
    .metric-value {
        font-size: 2rem;
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: 0.25rem;
        line-height: 1;
    }
    
    /* Section headers */
    .section-header {
        color: var(--text-primary);
        font-size: 1.5rem;
        font-weight: 600;
        margin: 2rem 0 1rem 0;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .section-header::after {
        content: '';
        flex: 1;
        height: 1px;
        background: linear-gradient(90deg, var(--accent-primary), transparent);
    }
    
    /* Enhanced tables */
    .stDataFrame {
        background: var(--bg-card);
        border-radius: 12px;
        overflow: hidden;
        border: 1px solid var(--border);
        box-shadow: var(--shadow);
    }
    
    .stDataFrame table {
        color: var(--text-primary) !important;
        background: transparent !important;
    }
    
    .stDataFrame table th {
        background: var(--bg-secondary) !important;
        color: var(--text-primary) !important;
        font-weight: 600;
        border: none !important;
        padding: 1rem !important;
    }
    
    .stDataFrame table td {
        border: none !important;
        padding: 0.75rem 1rem !important;
        background: var(--bg-card) !important;
    }
    
    .stDataFrame table tr:hover td {
        background: var(--bg-hover) !important;
    }
    
    /* Ratio column highlighting */
    .stDataFrame table td:contains("Ratio") {
        font-weight: 600 !important;
        color: var(--success) !important;
        background: rgba(16, 185, 129, 0.1) !important;
    }
    
    .stDataFrame table th:contains("Ratio") {
        color: var(--success) !important;
        background: rgba(16, 185, 129, 0.15) !important;
    }
    
    /* Modern buttons */
    .stButton button {
        background: var(--gradient-primary);
        color: white;
        border: none;
        border-radius: 8px;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        transition: all 0.3s ease;
        box-shadow: var(--shadow);
    }
    
    .stButton button:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(79, 70, 229, 0.3);
    }
    
    /* Download buttons */
    .stDownloadButton button {
        background: var(--gradient-primary);
        color: white;
        border: none;
        border-radius: 8px;
        font-weight: 500;
        padding: 0.5rem 1rem;
        transition: all 0.3s ease;
    }
    
    .stDownloadButton button:hover {
        background: var(--accent-secondary);
        transform: translateY(-1px);
    }
    
    /* Form styling */
    .stSelectbox > div > div {
        background: var(--bg-card);
        border: 1px solid var(--border);
        border-radius: 8px;
        color: var(--text-primary);
    }
    
    .stNumberInput > div > div {
        background: var(--bg-card);
        border: 1px solid var(--border);
        border-radius: 8px;
        color: var(--text-primary);
    }
    
    /* Tabs styling */
    .stTabs [data-baseweb="tab-list"] {
        gap: 8px;
        background: var(--bg-secondary);
        border-radius: 12px;
        padding: 0.5rem;
        margin-bottom: 2rem;
    }
    
    .stTabs [data-baseweb="tab"] {
        background: transparent;
        border-radius: 8px;
        color: var(--text-secondary);
        font-weight: 500;
        padding: 0.75rem 1.5rem;
        transition: all 0.3s ease;
    }
    
    .stTabs [data-baseweb="tab"]:hover {
        background: var(--bg-hover);
        color: var(--text-primary);
    }
    
    .stTabs [aria-selected="true"] {
        background: var(--gradient-primary);
        color: white !important;
        font-weight: 600;
    }
    

    
    /* Week selector container */
    .week-selector {
        background: var(--gradient-card);
        border: 1px solid var(--border);
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        box-shadow: var(--shadow);
        text-align: center;
    }
    
    /* Success/Warning/Info messages */
    .stSuccess {
        background: rgba(16, 185, 129, 0.1);
        border: 1px solid var(--success);
        border-radius: 8px;
        color: var(--text-primary);
    }
    
    .stWarning {
        background: rgba(245, 158, 11, 0.1);
        border: 1px solid var(--warning);
        border-radius: 8px;
        color: var(--text-primary);
    }
    
    .stInfo {
        background: rgba(79, 70, 229, 0.1);
        border: 1px solid var(--accent-primary);
        border-radius: 8px;
        color: var(--text-primary);
    }
    
    /* Loading spinner */
    .stSpinner > div {
        border-color: var(--accent-primary) transparent transparent transparent;
    }
    



    

</style>
""", unsafe_allow_html=True)

@st.cache_data
def load_data():
    """Load and cache the parquet data"""
    try:
        df = pd.read_parquet("24w28_25w27.parquet")
        df = df[~df['pmg'].isin(['HDL01'])]
        df = df.groupby(['country', 'week','dep', 'dep name', 'pmg', 'pmg_name', 'pmg_name_total'])[['sold_units', 'unit_delivered']].sum().reset_index()
        return df
    except FileNotFoundError:
        st.error("Data file '24w28_25w27.parquet' not found. Please ensure the file is in the correct directory.")
        return None

@st.cache_data
def get_available_weeks(df):
    """Cache available weeks calculation"""
    return sorted(df['week'].unique(), key=parse_custom_week)

@st.cache_data
def get_available_departments(df):
    """Cache available departments calculation"""
    return sorted(df['dep name'].dropna().unique().tolist())

@st.cache_data
def filter_data_by_weeks(df, selected_weeks):
    """Cache filtered data by weeks"""
    return df[df['week'].isin(selected_weeks)].copy()

@st.cache_data
def filter_data_by_department(df, department):
    """Cache filtered data by department"""
    if department != 'Total':
        return df[df['dep name'] == department].copy()
    return df.copy()

def parse_custom_week(week_str):
    """Parse custom week format to numeric value for sorting"""
    if pd.isna(week_str):
        return 0
    year = int(week_str[1:5])
    week = int(week_str[6:8])
    return year * 100 + week

def format_week_label(week_str):
    """Format week string for display"""
    if pd.isna(week_str):
        return ""
    year = week_str[3:5]
    week = week_str[6:8]
    return f"{year}w{week}"

def format_large_number(num):
    """Format large numbers with K/M suffixes"""
    if pd.isna(num):
        return "0"
    if num >= 1_000_000:
        return f"{num/1_000_000:.1f}M"
    elif num >= 1_000:
        return f"{num/1_000:.1f}K"
    else:
        return f"{num:.0f}"

def create_modern_metric_card(label, value, icon="📊"):
    """Create a modern metric card with hover effects"""
    return f"""
    <div class="metric-card">
        <div class="metric-label">{icon} {label}</div>
        <div class="metric-value">{value}</div>
    </div>
    """

@st.cache_data
def get_week_selection_data(df):
    """Cache week selection data preparation"""
    all_weeks = get_available_weeks(df)
    week_options = [format_week_label(week) for week in all_weeks]
    week_to_original = dict(zip(week_options, all_weeks))
    return all_weeks, week_options, week_to_original

def create_advanced_week_selector(df):
    """Create advanced week selection component at top of sidebar"""



    with st.sidebar.container():
        st.markdown('<div class="week-selector">📅 Week Selection</div>', unsafe_allow_html=True)

        # Use cached data
        all_weeks, week_options, week_to_original = get_week_selection_data(df)

        # Range selector with formatted labels
        col1, col2 = st.columns(2)

        with col1:
            start_week = st.selectbox(
                "Start Week:",
                week_options,
                index=0,
                key="start_week_selector"
            )

        with col2:
            end_week = st.selectbox(
                "End Week:",
                week_options,
                index=len(week_options)-1,
                key="end_week_selector"
            )

        # Get range of weeks
        start_idx = week_options.index(start_week)
        end_idx = week_options.index(end_week)

        if start_idx <= end_idx:
            selected_weeks = [week_to_original[week_options[i]] for i in range(start_idx, end_idx + 1)]
        else:
            st.error("Start week must be before or equal to end week")
            selected_weeks = all_weeks

        # Display selection summary
        if selected_weeks:
            st.markdown(f"**Selected:** {len(selected_weeks)} weeks")
            st.markdown(f"**Range:** {format_week_label(selected_weeks[0])} to {format_week_label(selected_weeks[-1])}")

        st.markdown('</div>', unsafe_allow_html=True)

    st.sidebar.divider()

    return selected_weeks if selected_weeks else all_weeks

def create_excel_download(table_data, is_pmg_level):
    """Create Excel file for download"""
    excel_buffer = io.BytesIO()

    with pd.ExcelWriter(excel_buffer, engine='openpyxl') as writer:
        sheet_name = "PMG_Analysis" if is_pmg_level else "Department_Analysis"
        table_data.to_excel(writer, sheet_name=sheet_name, index=False)

        worksheet = writer.sheets[sheet_name]

        try:
            from openpyxl.styles import Font, PatternFill

            ratio_cols = []
            for idx, col in enumerate(table_data.columns, 1):
                if 'Ratio %' in col:
                    ratio_cols.append(idx)

            blue_fill = PatternFill(start_color="E8F4F8", end_color="E8F4F8", fill_type="solid")
            bold_font = Font(bold=True, color="1F77B4")

            for col_idx in ratio_cols:
                for row_idx in range(2, len(table_data) + 2):
                    cell = worksheet.cell(row=row_idx, column=col_idx)
                    cell.fill = blue_fill
                    cell.font = bold_font

            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                worksheet.column_dimensions[column_letter].width = adjusted_width
        except ImportError:
            pass

    excel_buffer.seek(0)
    return excel_buffer.getvalue()

@st.cache_data
def prepare_weekly_data(filtered_df):
    """Cache weekly data preparation"""
    weekly_data = filtered_df.groupby(['dep name', 'country', 'week'])[['sold_units', 'unit_delivered']].sum().reset_index()
    weekly_data['delivery_ratio'] = (weekly_data['unit_delivered'] / weekly_data['sold_units'] * 100).round(2)
    return weekly_data

@st.cache_data
def create_recommendation_system(filtered_df, selected_weeks, required_period_length=12, selected_departments=None):
    """Create recommendation system to find optimal periods closest to 100% delivery ratio"""

    # Use cached weekly data preparation
    weekly_data = prepare_weekly_data(filtered_df)

    # Mode 1: Total (All Departments) - Original behavior
    if selected_departments is None:
        with st.spinner(f"🔍 Analyzing optimal {required_period_length}-week periods across all countries..."):
            departments = sorted(weekly_data['dep name'].unique())
            recommendations = []

            for dept in departments:
                dept_data = weekly_data[weekly_data['dep name'] == dept].copy()
                best_period = find_optimal_12week_period(dept_data, selected_weeks, required_period_length)

                if best_period:
                    distance_from_100 = abs(100 - best_period['overall_avg'])

                    cz_distance = abs(100 - best_period['avg_ratios']['CZ'])
                    hu_distance = abs(100 - best_period['avg_ratios']['HU'])
                    sk_distance = abs(100 - best_period['avg_ratios']['SK'])

                    recommendations.append({
                        'Department': dept,
                        'Optimal Period Start': format_week_label(best_period['start_week']),
                        'Optimal Period End': format_week_label(best_period['end_week']),
                        'Period Length': f'{required_period_length} weeks',
                        'CZ Ratio': f"{best_period['avg_ratios']['CZ']:.1f}%",
                        'CZ Distance': f"{cz_distance:.1f}%",
                        'HU Ratio': f"{best_period['avg_ratios']['HU']:.1f}%",
                        'HU Distance': f"{hu_distance:.1f}%",
                        'SK Ratio': f"{best_period['avg_ratios']['SK']:.1f}%",
                        'SK Distance': f"{sk_distance:.1f}%",
                        'Overall Average': f"{best_period['overall_avg']:.1f}%",
                        'Overall Distance': f"{distance_from_100:.1f}%",
                        'Optimization Score': f"{best_period['score']:.2f}"
                    })

            if recommendations:
                recommendations_df = pd.DataFrame(recommendations)
                recommendations_df['_sort_score'] = recommendations_df['Optimization Score'].astype(float)
                recommendations_df = recommendations_df.sort_values('_sort_score').drop('_sort_score', axis=1)

                return {
                    'recommendations_df': recommendations_df,
                    'recommendations': recommendations,
                    'period_length': required_period_length,
                    'analysis_mode': 'total'
                }
            else:
                return {
                    'recommendations_df': None,
                    'recommendations': [],
                    'period_length': required_period_length,
                    'analysis_mode': 'total',
                    'error': f"No sufficient {required_period_length}-week periods found for recommendation analysis. Try selecting a longer time range (at least {required_period_length} weeks)."
                }
    
    # Mode 2: Selected Departments - Find common optimal periods
    else:
        with st.spinner(f"🔍 Finding common optimal {required_period_length}-week periods for {len(selected_departments)} selected departments..."):
            best_common_period = find_common_optimal_periods(weekly_data, selected_departments, selected_weeks, required_period_length)

            if best_common_period:
                # Create recommendations table for selected departments
                recommendations = []

                for dept in selected_departments:
                    dept_ratios = best_common_period['dept_ratios'][dept]
                    dept_avg = dept_ratios['avg_ratio']
                    distance_from_100 = abs(100 - dept_avg)

                    cz_ratio = dept_ratios['country_ratios']['CZ']
                    hu_ratio = dept_ratios['country_ratios']['HU']
                    sk_ratio = dept_ratios['country_ratios']['SK']

                    cz_distance = abs(100 - cz_ratio)
                    hu_distance = abs(100 - hu_ratio)
                    sk_distance = abs(100 - sk_ratio)

                    recommendations.append({
                        'Department': dept,
                        'Optimal Period Start': format_week_label(best_common_period['start_week']),
                        'Optimal Period End': format_week_label(best_common_period['end_week']),
                        'Period Length': f'{required_period_length} weeks',
                        'CZ Ratio': f"{cz_ratio:.1f}%",
                        'CZ Distance': f"{cz_distance:.1f}%",
                        'HU Ratio': f"{hu_ratio:.1f}%",
                        'HU Distance': f"{hu_distance:.1f}%",
                        'SK Ratio': f"{sk_ratio:.1f}%",
                        'SK Distance': f"{sk_distance:.1f}%",
                        'Department Average': f"{dept_avg:.1f}%",
                        'Department Distance': f"{distance_from_100:.1f}%",
                        'Department Score': f"{best_common_period['dept_scores'][dept]:.2f}"
                    })

                recommendations_df = pd.DataFrame(recommendations)

                return {
                    'recommendations_df': recommendations_df,
                    'recommendations': recommendations,
                    'period_length': required_period_length,
                    'analysis_mode': 'selected',
                    'selected_departments': selected_departments,
                    'common_period': best_common_period,
                    'overall_avg': best_common_period['overall_avg'],
                    'overall_std': best_common_period['overall_std']
                }
            else:
                return {
                    'recommendations_df': None,
                    'recommendations': [],
                    'period_length': required_period_length,
                    'analysis_mode': 'selected',
                    'selected_departments': selected_departments,
                    'error': f"No sufficient {required_period_length}-week periods found where all selected departments have complete data. Try selecting a longer time range or fewer departments."
                }

def display_recommendation_results(results_data, period_length):
    """Display recommendation results with Excel download functionality"""
    
    if 'error' in results_data:
        st.warning(f"⚠️ {results_data['error']}")
        return
    
    recommendations_df = results_data['recommendations_df']
    recommendations = results_data['recommendations']
    analysis_mode = results_data.get('analysis_mode', 'total')
    
    if recommendations_df is not None and len(recommendations) > 0:
        def highlight_ratio_columns(df):
            styles = pd.DataFrame('', index=df.index, columns=df.columns)

            # Different column highlighting based on analysis mode
            if analysis_mode == 'total':
                ratio_columns = ['CZ Ratio', 'HU Ratio', 'SK Ratio', 'Overall Average']
                distance_columns = ['CZ Distance', 'HU Distance', 'SK Distance', 'Overall Distance']
            else:  # selected departments mode
                ratio_columns = ['CZ Ratio', 'HU Ratio', 'SK Ratio', 'Department Average']
                distance_columns = ['CZ Distance', 'HU Distance', 'SK Distance', 'Department Distance']
            
            for col in ratio_columns:
                if col in df.columns:
                    styles[col] = 'font-weight: bold; background-color: rgba(16, 185, 129, 0.15); color: #10b981;'
            
            for col in distance_columns:
                if col in df.columns:
                    styles[col] = 'font-weight: bold; background-color: rgba(245, 158, 11, 0.15); color: #f59e0b;'

            return styles

        styled_recommendations = recommendations_df.style.apply(highlight_ratio_columns, axis=None)
        st.dataframe(styled_recommendations, use_container_width=True, height=400, hide_index=True)

        # Download button
        filename_suffix = "total" if analysis_mode == 'total' else f"selected_{len(results_data.get('selected_departments', []))}_depts"
        excel_buffer = create_excel_download(recommendations_df, is_pmg_level=False)
        st.download_button(
            label="📥 Download Recommendations as Excel",
            data=excel_buffer,
            file_name=f"optimal_periods_{filename_suffix}_{period_length}weeks_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
            mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        )

        # Display analysis-specific summary
        if analysis_mode == 'total':
            best_dept = min(recommendations, key=lambda x: float(x['Optimization Score']))
            st.success(f"🎯 **Most Optimal Performance:** {best_dept['Department']} during {best_dept['Optimal Period Start']} to {best_dept['Optimal Period End']} with {best_dept['Overall Average']} average delivery ratio (only {best_dept['Overall Distance']} away from perfect 100%)")
            st.info(f"📊 **Analysis Summary:** Found optimal {period_length}-week periods for {len(recommendations)} departments. Periods are ranked by proximity to 100% delivery ratio across all countries (CZ, HU, SK).")
        
        else:  # selected departments mode
            selected_depts = results_data.get('selected_departments', [])
            overall_avg = results_data.get('overall_avg', 0)
            overall_std = results_data.get('overall_std', 0)
            
            # Find the period info from first recommendation
            period_start = recommendations[0]['Optimal Period Start']
            period_end = recommendations[0]['Optimal Period End']
            
            st.success(f"🎯 **Common Optimal Period:** {period_start} to {period_end} works best for all {len(selected_depts)} selected departments with {overall_avg:.1f}% average delivery ratio across all countries and departments.")
            
            # Show department performance summary
            dept_averages = [float(rec['Department Average'].replace('%', '')) for rec in recommendations]
            best_dept_avg = max(dept_averages)
            worst_dept_avg = min(dept_averages)
            
            st.info(f"📊 **Selected Departments Analysis:** Found common {period_length}-week period for {', '.join(selected_depts)}. Performance range: {worst_dept_avg:.1f}% - {best_dept_avg:.1f}% (σ={overall_std:.1f}%)")
            
            # Show consistency metric
            if overall_std < 5:
                st.success("✅ **High Consistency:** All departments perform very similarly in this period (low variation)")
            elif overall_std < 10:
                st.warning("⚠️ **Moderate Consistency:** Some variation in department performance during this period")
            else:
                st.error("❌ **Low Consistency:** Significant variation in department performance - consider selecting fewer departments")

@st.cache_data
def find_common_optimal_periods(weekly_data, selected_departments, selected_weeks, required_period_length=12):
    """Find optimal periods that work well for ALL selected departments simultaneously"""

    countries = ['CZ', 'HU', 'SK']

    if len(selected_weeks) < required_period_length:
        return None

    # Filter data for selected departments
    weekly_data = weekly_data[weekly_data['dep name'].isin(selected_departments)]
    
    best_periods = []
    
    # Analyze each possible period
    for start_idx in range(len(selected_weeks) - required_period_length + 1):
        end_idx = start_idx + required_period_length - 1
        period_weeks = selected_weeks[start_idx:end_idx + 1]
        
        if len(period_weeks) != required_period_length:
            continue
        
        # Check if this period works for ALL selected departments
        period_valid = True
        dept_scores = {}
        all_dept_ratios = {}
        
        for dept in selected_departments:
            dept_data = weekly_data[weekly_data['dep name'] == dept]
            dept_period_ratios = {}
            valid_countries = 0
            
            for country in countries:
                country_data = dept_data[
                    (dept_data['country'] == country) &
                    (dept_data['week'].isin(period_weeks))
                ]
                
                if len(country_data) == required_period_length:
                    avg_ratio = country_data['delivery_ratio'].mean()
                    if not pd.isna(avg_ratio) and avg_ratio > 0:
                        dept_period_ratios[country] = avg_ratio
                        valid_countries += 1
            
            # Department must have data for all countries in this period
            if valid_countries == len(countries):
                dept_avg = np.mean(list(dept_period_ratios.values()))
                ratio_values = list(dept_period_ratios.values())
                
                # Calculate department score
                individual_distances = [abs(100 - ratio) for ratio in ratio_values]
                individual_score = sum(individual_distances)
                std_dev = np.std(ratio_values)
                balance_penalty = std_dev * 0.5
                
                # Extreme penalty
                extreme_penalty = 0
                for ratio in ratio_values:
                    if ratio < 50 or ratio > 150:
                        extreme_penalty += 100
                    elif ratio < 70 or ratio > 130:
                        extreme_penalty += 25
                
                dept_score = individual_score + balance_penalty + extreme_penalty
                dept_scores[dept] = dept_score
                all_dept_ratios[dept] = {
                    'country_ratios': dept_period_ratios,
                    'avg_ratio': dept_avg
                }
            else:
                period_valid = False
                break
        
        if period_valid and len(dept_scores) == len(selected_departments):
            # Calculate combined score for this period
            combined_score = sum(dept_scores.values()) / len(dept_scores)
            
            # Calculate overall statistics
            all_ratios = []
            for dept_data in all_dept_ratios.values():
                all_ratios.extend(list(dept_data['country_ratios'].values()))
            
            overall_avg = np.mean(all_ratios)
            overall_std = np.std(all_ratios)
            
            # Add consistency bonus (lower std is better)
            consistency_bonus = max(0, 10 - overall_std)
            final_score = combined_score - consistency_bonus
            
            best_periods.append({
                'start_week': period_weeks[0],
                'end_week': period_weeks[-1],
                'length': required_period_length,
                'departments': selected_departments,
                'dept_ratios': all_dept_ratios,
                'overall_avg': overall_avg,
                'overall_std': overall_std,
                'score': final_score,
                'dept_scores': dept_scores
            })
    
    # Return the best period
    if best_periods:
        return min(best_periods, key=lambda x: x['score'])
    return None

@st.cache_data
def find_optimal_12week_period(dept_data, selected_weeks, required_period_length=12):
    """Find the optimal consecutive period closest to 100% delivery ratio across all countries"""

    countries = ['CZ', 'HU', 'SK']

    if len(selected_weeks) < required_period_length:
        return None

    best_period = None
    best_score = float('inf')

    for start_idx in range(len(selected_weeks) - required_period_length + 1):
        end_idx = start_idx + required_period_length - 1
        period_weeks = selected_weeks[start_idx:end_idx + 1]

        if len(period_weeks) != required_period_length:
            continue

        period_ratios = {}
        valid_countries = 0

        for country in countries:
            country_data = dept_data[
                (dept_data['country'] == country) &
                (dept_data['week'].isin(period_weeks))
            ]

            if len(country_data) == required_period_length:
                avg_ratio = country_data['delivery_ratio'].mean()
                if not pd.isna(avg_ratio) and avg_ratio > 0:
                    period_ratios[country] = avg_ratio
                    valid_countries += 1

        if valid_countries == len(countries):
            overall_avg = np.mean(list(period_ratios.values()))
            ratio_values = list(period_ratios.values())

            individual_distances = [abs(100 - ratio) for ratio in ratio_values]
            individual_score = sum(individual_distances)
            
            std_dev = np.std(ratio_values)
            balance_penalty = std_dev * 0.5
            
            extreme_penalty = 0
            for ratio in ratio_values:
                if ratio < 50 or ratio > 150:
                    extreme_penalty += 100
                elif ratio < 70 or ratio > 130:
                    extreme_penalty += 25
            
            final_score = individual_score + balance_penalty + extreme_penalty

            if final_score < best_score:
                best_score = final_score
                best_period = {
                    'start_week': period_weeks[0],
                    'end_week': period_weeks[-1],
                    'length': required_period_length,
                    'avg_ratios': period_ratios,
                    'overall_avg': overall_avg,
                    'score': final_score,
                    'individual_distances': individual_distances,
                    'balance_score': std_dev
                }

    return best_period

def add_trendline(x, y):
    """Calculate trendline for the data - returns x and y values for the trendline"""
    if len(x) < 2 or len(y) < 2:
        return x, [0] * len(x)

    x_numeric = x.apply(parse_custom_week)
    try:
        valid_indices = ~(pd.isna(x_numeric) | pd.isna(y))
        if valid_indices.sum() < 2:
            return x, [0] * len(x)

        x_clean = x_numeric[valid_indices]
        y_clean = y[valid_indices]

        slope, intercept, _, _, _ = stats.linregress(x_clean, y_clean)
        line = slope * x_numeric + intercept
        return x, line.fillna(0).tolist()
    except Exception as e:
        return x, [0] * len(x)

@st.cache_data
def create_modern_plotly_chart(chart_data, countries, selected_weeks):
    """Create modern dark-themed Plotly chart with proper spacing and no overlaps"""

    # Professional color palette for countries
    colors = {
        'CZ': ['#4f46e5', '#6366f1'],
        'HU': ['#8b5cf6', '#a855f7'],
        'SK': ['#10b981', '#34d399']
    }
    
    # Calculate proper spacing based on number of countries
    vertical_spacing = 0.08 if len(countries) > 2 else 0.05
    
    fig = make_subplots(
        rows=len(countries), cols=1,
        shared_xaxes=True,
        vertical_spacing=vertical_spacing,
        subplot_titles=None  # We'll add country labels differently to avoid overlap
    )
    
    for i, country in enumerate(countries, start=1):
        country_data = chart_data[chart_data['country'] == country].copy()
        
        week_labels = country_data["week"].apply(format_week_label)
        
        # Enhanced bar charts with gradients
        fig.add_trace(
            go.Bar(
                x=week_labels, 
                y=country_data["sold_units"],
                name="Sold Units" if i==1 else "", 
                marker=dict(
                    color=colors[country][0],
                    opacity=0.8,
                    line=dict(width=0)
                ),
                showlegend=(i==1), 
                hovertemplate="<b>Sold Units</b><br>Week: %{x}<br>Units: %{customdata}<extra></extra>",
                customdata=[format_large_number(x) for x in country_data["sold_units"]],
                offset=-0.2, 
                width=0.35
            ),
            row=i, col=1
        )
        
        fig.add_trace(
            go.Bar(
                x=week_labels, 
                y=country_data["unit_delivered"],
                name="Units Delivered" if i==1 else "", 
                marker=dict(
                    color=colors[country][1],
                    opacity=0.8,
                    line=dict(width=0)
                ),
                showlegend=(i==1), 
                hovertemplate="<b>Units Delivered</b><br>Week: %{x}<br>Units: %{customdata}<extra></extra>",
                customdata=[format_large_number(x) for x in country_data["unit_delivered"]],
                offset=0.2, 
                width=0.35
            ),
            row=i, col=1
        )
        
        # Enhanced trendlines
        for column, color, name in [("sold_units", "#f59e0b", "Sold Units Trend"), 
                                    ("unit_delivered", "#ef4444", "Units Delivered Trend")]:
            x_trend, y_trend = add_trendline(country_data["week"], country_data[column])
            fig.add_trace(
                go.Scatter(
                    x=x_trend.apply(format_week_label), 
                    y=y_trend, 
                    mode='lines', 
                    name=name if i==1 else "",
                    line=dict(
                        color=color, 
                        width=2, 
                        dash='solid',
                        shape='spline'
                    ), 
                    showlegend=(i==1),
                    hovertemplate=f"<b>{name}</b><br>Week: %{{x}}<br>Units: %{{customdata}}<extra></extra>",
                    customdata=[format_large_number(x) for x in y_trend]
                ),
                row=i, col=1
            )
    
    # Modern dark theme layout with better spacing
    fig.update_layout(
        title=dict(
            text="<b style='color: #ffffff; font-size: 24px'>Sales and Deliveries Analysis</b>",
            x=0.5,
            xanchor='center',
            font=dict(family="Inter, sans-serif"),
            y=0.98
        ),
        barmode='overlay',
        bargap=0.15,
        bargroupgap=0.1,
        legend=dict(
            orientation="h", 
            yanchor="bottom", 
            y=1.0, 
            xanchor="center", 
            x=0.5,
            font=dict(color='white', size=11, family="Inter, sans-serif"),
            bgcolor="rgba(42, 45, 74, 0.9)",
            bordercolor="#374151",
            borderwidth=1
        ),
        showlegend=True,
        margin=dict(l=60, r=120, t=80, b=50),
        plot_bgcolor='rgba(10, 14, 26, 0)',
        paper_bgcolor='rgba(10, 14, 26, 0)',
        font=dict(family="Inter, sans-serif", size=11, color="white"),
        hoverlabel=dict(
            bgcolor="rgba(42, 45, 74, 0.95)",
            font_size=11,
            font_color="white",
            bordercolor="#374151"
        ),
        height=max(600, 200 * len(countries))
    )
    
    # Enhanced axes styling
    fig.update_xaxes(
        title_text="",
        tickangle=45,
        gridcolor='rgba(55, 65, 81, 0.3)',
        tickfont=dict(size=12, color='#b8bcc8', family="Inter, sans-serif"),
        showline=True,
        linecolor='#374151',
        linewidth=1
    )
    
    fig.update_yaxes(
        title_text="Units",
        gridcolor='rgba(55, 65, 81, 0.3)',
        tickformat=',d',
        title_font=dict(size=11, color='#b8bcc8', family="Inter, sans-serif"),
        tickfont=dict(size=10, color='#b8bcc8', family="Inter, sans-serif"),
        showline=True,
        linecolor='#374151',
        linewidth=1
    )
    
    # Add country labels on the right side to avoid overlaps
    for i, country in enumerate(countries):
        # Calculate the y position for each country label
        y_pos = 1 - (i / len(countries)) - (1 / (len(countries) * 2))
        
        fig.add_annotation(
            text=f"<b>{country}</b>",
            xref="paper", 
            yref="paper",
            x=1.05, 
            y=y_pos,
            showarrow=False,
            font=dict(size=14, color=colors[country][0], family="Inter, sans-serif"),
            xanchor="left", 
            yanchor="middle"
        )
    
    return fig

def main():
    # Modern header
    st.markdown('<h1 class="main-header">Sales & Deliveries for next year Productivity datasets</h1>', unsafe_allow_html=True)

    # Load data
    df = load_data()
    if df is None:
        return

    # Week selection
    selected_weeks = create_advanced_week_selector(df)

    # Modern tabs
    tab1, tab2 = st.tabs(["🌍 Country Analysis", "🏢 Department Analysis"])

    with tab1:
        country_analysis_tab(df, selected_weeks)

    with tab2:
        department_analysis_tab(df, selected_weeks)

@st.cache_data
def calculate_summary_metrics(filtered_df):
    """Cache summary metrics calculation"""
    total_sold = filtered_df['sold_units'].sum()
    total_delivered = filtered_df['unit_delivered'].sum()
    delivery_ratio = (total_delivered / total_sold * 100) if total_sold > 0 else 0
    return total_sold, total_delivered, delivery_ratio

@st.cache_data
def prepare_chart_data(filtered_df, selected_department):
    """Cache chart data preparation"""
    if selected_department == 'Total':
        chart_data = filtered_df[(filtered_df['dep'] != 'UNA') & (filtered_df['pmg'] != 'HDL01')].groupby(['country', 'week'])[['sold_units', 'unit_delivered']].sum().reset_index()
    else:
        chart_data = filtered_df.groupby(['country', 'week'])[['sold_units', 'unit_delivered']].sum().reset_index()
    return chart_data

def country_analysis_tab(df, selected_weeks):
    # Sidebar controls in a clean container
    with st.sidebar.container():
        st.markdown('<div class="section-header">🔧 Country Analysis</div>', unsafe_allow_html=True)

        # Department filter with better spacing - use cached departments
        departments = ['Total'] + get_available_departments(df)
        selected_department = st.selectbox(
            "Select Department:",
            departments,
            index=0,
            help="Choose a specific department or 'Total' for all departments"
        )

        st.divider()

    # Use cached filtering functions
    filtered_df = filter_data_by_weeks(df, selected_weeks)
    filtered_df = filter_data_by_department(filtered_df, selected_department)
    
    # Main content with proper containers
    if len(filtered_df) == 0:
        st.warning("⚠️ No data available for the selected filters.")
        return
    
    # Metrics section with better container structure
    with st.container():
        st.markdown('<div class="section-header">📈 Summary Metrics</div>', unsafe_allow_html=True)

        # Use consistent column ratios
        col1, col2, col3 = st.columns([1, 1, 1])

        # Use cached metrics calculation
        total_sold, total_delivered, delivery_ratio = calculate_summary_metrics(filtered_df)
        
        with col1:
            st.markdown(
                create_modern_metric_card(
                    "Total Sold Units", 
                    format_large_number(total_sold),
                    "🛒"
                ), 
                unsafe_allow_html=True
            )
        
        with col2:
            st.markdown(
                create_modern_metric_card(
                    "Total Delivered Units", 
                    format_large_number(total_delivered),
                    "🚚"
                ), 
                unsafe_allow_html=True
            )
        
        with col3:
            # Use the new professional color scheme
            if delivery_ratio >= 95:
                ratio_color = "#10b981"  # Success green
            elif delivery_ratio >= 85:
                ratio_color = "#f59e0b"  # Warning amber
            else:
                ratio_color = "#ef4444"  # Error red
                
            st.markdown(
                create_modern_metric_card(
                    "Delivery Ratio", 
                    f"<span style='color: {ratio_color}'>{delivery_ratio:.1f}%</span>",
                    "📊"
                ), 
                unsafe_allow_html=True
            )

    # Add some spacing
    st.markdown("<br>", unsafe_allow_html=True)
    
    # Chart section with proper container
    with st.container():
        st.markdown('<div class="section-header">📊 Trend Analysis</div>', unsafe_allow_html=True)

        # Use cached chart data preparation
        chart_data = prepare_chart_data(filtered_df, selected_department)
        countries = sorted(chart_data['country'].unique())

        if len(countries) > 0:
            fig = create_modern_plotly_chart(chart_data, countries, selected_weeks)
            st.plotly_chart(fig, use_container_width=True, key="country_chart")
        else:
            st.info("📊 No chart data available for the selected filters.")

def department_analysis_tab(df, selected_weeks):
    """Department analysis tab with modern table view and better structure"""

    # Sidebar controls in a clean container
    with st.sidebar.container():
        st.markdown('<div class="section-header">🔧 Department Analysis</div>', unsafe_allow_html=True)

        # PMG breakdown toggle with better styling
        show_pmg_breakdown = st.checkbox(
            "Show PMG Level",
            value=False,
            help="When enabled, shows PMG-level breakdown by country instead of department level"
        )

        st.divider()

    # Use cached filtering
    filtered_df = filter_data_by_weeks(df, selected_weeks)

    if len(filtered_df) == 0:
        st.warning("⚠️ No data available for the selected filters.")
        return

    # Table section with proper container
    with st.container():
        # Create and display the table
        if show_pmg_breakdown:
            st.markdown('<div class="section-header">📊 All Departments - PMG Level</div>', unsafe_allow_html=True)
            table_data = create_all_pmg_table_by_country(filtered_df)
        else:
            st.markdown('<div class="section-header">📊 All Departments Summary</div>', unsafe_allow_html=True)
            table_data = create_all_departments_table_by_country(filtered_df)

        # Excel download with better positioning
        if table_data is not None:
            col1, col2, col3 = st.columns([2, 1, 2])
            with col2:
                excel_buffer = create_excel_download(table_data, show_pmg_breakdown)
                st.download_button(
                    label="📥 Download as Excel",
                    data=excel_buffer,
                    file_name=f"department_analysis_{'pmg' if show_pmg_breakdown else 'dept'}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
                    mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    use_container_width=True
                )

    # Add spacing
    st.markdown("<br>", unsafe_allow_html=True)

    # Recommendation system with proper container
    with st.container():
        st.markdown('<div class="section-header">🎯 WEEKS Recommendations</div>', unsafe_allow_html=True)
        st.markdown("**Find optimal periods where delivery ratios are closest to 100% across all countries**")

        # Enhanced session state for recommendations with cache invalidation
        if 'recommendations_data' not in st.session_state:
            st.session_state.recommendations_data = None
        if 'recommendations_period_length' not in st.session_state:
            st.session_state.recommendations_period_length = 12
        if 'recommendations_cache_key' not in st.session_state:
            st.session_state.recommendations_cache_key = None



        # Get available departments using cached function
        available_departments = get_available_departments(filtered_df)

        # Create columns for analysis mode and help popover
        col1, col2 = st.columns([3, 1])

        with col1:
            # Analysis mode selection
            analysis_mode = st.radio(
                "Analysis Mode:",
                ["Best Periods (By Departments)", "Selected Departments"],
                help="Choose whether to analyze all departments individually or find common optimal periods for selected departments"
            )

        with col2:
            # Add popover with explanation of how the recommendation system works
            with st.popover("ℹ️ How it works"):
                st.markdown("""
                **Recommendation System Overview**

                Our system analyzes historical delivery performance to identify optimal time periods for business planning and forecasting.

                **What it does:**
                - Examines delivery ratios across all countries (CZ, HU, SK)
                - Identifies consecutive week periods with performance closest to 100% delivery ratio
                - Ranks periods by how consistently they achieve target performance

                **How it determines recommendations:**
                - Calculates average delivery ratios for each country during each period
                - Measures distance from the ideal 100% delivery target
                - Applies scoring that rewards consistency across countries
                - Penalizes extreme variations or poor performance

                **Key factors considered:**
                - **Proximity to 100%**: Periods closer to perfect delivery are preferred
                - **Cross-country consistency**: Balanced performance across CZ, HU, SK
                - **Stability**: Lower variation in delivery ratios within the period
                - **Data completeness**: Only periods with full data coverage

                **How to interpret results:**
                - **Lower "Distance" values** = Better performance (closer to 100%)
                - **Lower "Optimization Score"** = More optimal period overall
                - **Period recommendations** show the best consecutive weeks for planning

                **Business application:**
                Use these insights to identify stable periods for demand forecasting, capacity planning, and setting realistic delivery targets for your teams.
                """)

        
        selected_departments = []
        if analysis_mode == "Selected Departments":
            selected_departments = st.multiselect(
                "Select Departments:",
                options=available_departments,
                help="Select departments to find common optimal periods. The system will find periods where ALL selected departments perform well together."
            )
            
            if not selected_departments:
                st.warning("⚠️ Please select at least one department for analysis.")
        
        st.divider()

        # Modern form with better layout
        with st.form("recommendation_form", clear_on_submit=False):
            
            col_1, col_2 = st.columns([2, 1])

            with col_1:
                required_period_length = st.number_input(
                    "Period Length (weeks):",
                    min_value=1,
                    max_value=52,
                    value=st.session_state.recommendations_period_length,
                    step=1,
                    help="Number of consecutive weeks to analyze for optimal periods"
                )
            with col_2:
                st.markdown("<br>", unsafe_allow_html=True)
                submitted = st.form_submit_button(
                    "🔍 Analyze Optimal Periods", 
                    use_container_width=True,
                    type="primary"
                )

            if submitted:
                # Validate selection for Selected Departments mode
                if analysis_mode == "Best Periods (By Departments)" and not selected_departments:
                    selected_departments = None
                elif analysis_mode == "Selected Departments" and not selected_departments:
                    st.error("❌ Please select at least one department for analysis.")
                    st.stop()

                # Create cache key for invalidation
                cache_key = f"{analysis_mode}_{required_period_length}_{len(selected_weeks)}_{hash(tuple(selected_departments) if selected_departments else 'total')}"

                # Only recalculate if cache key changed
                if st.session_state.recommendations_cache_key != cache_key:
                    st.session_state.recommendations_period_length = required_period_length
                    st.session_state.recommendations_cache_key = cache_key

                    # Pass department selection to recommendation system
                    if analysis_mode == "Best Periods (By Departments)":
                        st.session_state.recommendations_data = create_recommendation_system(
                            filtered_df, selected_weeks, required_period_length, selected_departments=None
                        )
                    else:
                        st.session_state.recommendations_data = create_recommendation_system(
                            filtered_df, selected_weeks, required_period_length, selected_departments=selected_departments
                        )

        # Display results
        if st.session_state.recommendations_data is not None:
            display_recommendation_results(st.session_state.recommendations_data, st.session_state.recommendations_period_length)

@st.cache_data
def create_all_departments_table_by_country(filtered_df):
    """Create modern table showing all departments with country breakdown"""

    dept_summary = filtered_df.groupby(['dep name', 'country'])[['sold_units', 'unit_delivered']].sum().reset_index()
    dept_summary['delivery_ratio'] = (dept_summary['unit_delivered'] / dept_summary['sold_units'] * 100).round(1)

    sold_pivot = dept_summary.pivot(index='dep name', columns='country', values='sold_units').fillna(0)
    delivered_pivot = dept_summary.pivot(index='dep name', columns='country', values='unit_delivered').fillna(0)
    ratio_pivot = dept_summary.pivot(index='dep name', columns='country', values='delivery_ratio').fillna(0)

    combined_data = []
    for dept in sold_pivot.index:
        row = {'Department': dept}
        for country in ['CZ', 'HU', 'SK']:
            if country in sold_pivot.columns:
                sold = sold_pivot.loc[dept, country]
                delivered = delivered_pivot.loc[dept, country]
                ratio = ratio_pivot.loc[dept, country]

                row[f'{country} - Sold Units'] = f"{sold:,.0f}"
                row[f'{country} - Delivered Units'] = f"{delivered:,.0f}"
                row[f'{country} - Ratio %'] = f"{ratio:.1f}%"
        combined_data.append(row)

    result_df = pd.DataFrame(combined_data)

    def highlight_ratio_columns(df):
        styles = pd.DataFrame('', index=df.index, columns=df.columns)

        for col in df.columns:
            if 'Ratio %' in col:
                styles[col] = 'font-weight: bold; background-color: rgba(16, 185, 129, 0.15); color: #10b981;'

        return styles

    styled_df = result_df.style.apply(highlight_ratio_columns, axis=None)
    st.dataframe(styled_df, use_container_width=True, height=500, hide_index=True)

    return result_df

@st.cache_data
def create_all_pmg_table_by_country(filtered_df):
    """Create modern table showing all PMGs with country breakdown"""

    if 'pmg_name_total' not in filtered_df.columns:
        st.warning("PMG data not available in the dataset.")
        return None

    pmg_summary = filtered_df.groupby(['dep name', 'pmg_name_total', 'country'])[['sold_units', 'unit_delivered']].sum().reset_index()
    pmg_summary['delivery_ratio'] = (pmg_summary['unit_delivered'] / pmg_summary['sold_units'] * 100).round(1)

    sold_pivot = pmg_summary.pivot_table(index=['dep name', 'pmg_name_total'], columns='country', values='sold_units', fill_value=0)
    delivered_pivot = pmg_summary.pivot_table(index=['dep name', 'pmg_name_total'], columns='country', values='unit_delivered', fill_value=0)
    ratio_pivot = pmg_summary.pivot_table(index=['dep name', 'pmg_name_total'], columns='country', values='delivery_ratio', fill_value=0)

    combined_data = []
    for (dept, pmg) in sold_pivot.index:
        row = {'Department': dept, 'PMG Name': pmg}
        for country in ['CZ', 'HU', 'SK']:
            if country in sold_pivot.columns:
                sold = sold_pivot.loc[(dept, pmg), country]
                delivered = delivered_pivot.loc[(dept, pmg), country]
                ratio = ratio_pivot.loc[(dept, pmg), country]

                row[f'{country} - Sold Units'] = f"{sold:,.0f}"
                row[f'{country} - Delivered Units'] = f"{delivered:,.0f}"
                row[f'{country} - Ratio %'] = f"{ratio:.1f}%"
        combined_data.append(row)

    result_df = pd.DataFrame(combined_data)

    def highlight_ratio_columns(df):
        styles = pd.DataFrame('', index=df.index, columns=df.columns)

        for col in df.columns:
            if 'Ratio %' in col:
                styles[col] = 'font-weight: bold; background-color: rgba(16, 185, 129, 0.15); color: #10b981;'

        return styles

    styled_df = result_df.style.apply(highlight_ratio_columns, axis=None)
    st.dataframe(styled_df, use_container_width=True, height=600, hide_index=True)

    return result_df

if __name__ == "__main__":
    main()